// src/services/paypalService.js
import { TierService as TierServiceD1 } from "./tierServiceD1.js";

export class PayPalService {
  constructor(env) {
    this.env = env;
    this.clientId = env.PAYPAL_CLIENT_ID;
    this.clientSecret = env.PAYPAL_CLIENT_SECRET;
    this.baseURL =
      env.PAYPAL_SANDBOX === "true"
        ? "https://api-m.sandbox.paypal.com"
        : "https://api-m.paypal.com";
    this.tierService = new TierServiceD1(env);
  }

  async createProduct(tierName) {
    try {
      const accessToken = await this.getAccessToken();
      const productData = {
        name: `${tierName} Subscription`,
        description: `Access to ${tierName} tier features`,
        type: "SERVICE",
        category: "SOFTWARE",
      };

      const response = await fetch(`${this.baseURL}/v1/catalogs/products`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "PayPal-Request-Id": `prod_${Date.now()}`,
        },
        body: JSON.stringify(productData),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create product");
      }

      return data.id;
    } catch (error) {
      console.error("Error creating PayPal product:", error);
      throw error;
    }
  }

  async createSubscription(
    userId,
    tier,
    addons = { addon1: false, addon2: false }
  ) {
    try {
      console.log(
        "Creating subscription for tier:",
        tier,
        "with addons:",
        addons
      );

      // Get tier settings to get the price
      const tierSettings = await this.tierService.getTierSettings();
      console.log("Tier settings:", tierSettings);

      if (!tierSettings?.config?.[tier]) {
        throw new Error(`Invalid tier: ${tier}`);
      }

      const tierConfig = tierSettings.config[tier];
      let basePrice = tierConfig.price;

      // Calculate total price including selected add-ons
      let totalPrice = basePrice;
      if (addons.addon1) {
        totalPrice += tierConfig.addon1_price;
      }
      if (addons.addon2) {
        totalPrice += tierConfig.addon2_price;
      }

      // Create PayPal product with add-ons information
      const productName = `${tierConfig.name}${
        addons.addon1 ? " + Addon 1" : ""
      }${addons.addon2 ? " + Addon 2" : ""}`;
      const productDescription = this._generateProductDescription(
        tierConfig,
        addons
      );

      const productId = await this.createProduct(
        productName,
        productDescription
      );

      // Create billing plan with the new product
      const planData = {
        name: `${productName} Monthly Subscription`,
        product_id: productId,
        billing_cycles: [
          {
            frequency: {
              interval_unit: "MONTH",
              interval_count: 1,
            },
            tenure_type: "REGULAR",
            sequence: 1,
            total_cycles: 0,
            pricing_scheme: {
              fixed_price: {
                value: totalPrice.toFixed(2),
                currency_code: "USD",
              },
            },
          },
        ],
        payment_preferences: {
          auto_bill_outstanding: true,
          setup_fee_failure_action: "CONTINUE",
          payment_failure_threshold: 3,
        },
      };

      // Create plan and subscription
      const plan = await this._createPlan(planData);
      const subscription = await this._createSubscriptionWithPlan(
        plan.id,
        userId
      );

      // Store subscription details in KV with add-ons information
      const subscriptionRecord = {
        subscriptionId: subscription.id,
        planId: plan.id,
        productId: productId,
        userId: userId,
        tier,
        basePrice: basePrice,
        addons: {
          addon1: addons.addon1
            ? {
                enabled: true,
                price: tierConfig.addon1_price,
                features: tierConfig.addon1_detail,
              }
            : false,
          addon2: addons.addon2
            ? {
                enabled: true,
                price: tierConfig.addon2_price,
                features: tierConfig.addon2_detail,
              }
            : false,
        },
        totalPrice: totalPrice,
        status: subscription.status,
        createdAt: new Date().toISOString(),
      };

      // Extract ba_token from the approval URL
      const approvalUrl = new URL(
        subscription.links.find((link) => link.rel === "approve")?.href
      );
      const baToken = approvalUrl.searchParams.get("ba_token");

      if (baToken) {
        // Store a mapping between ba_token and subscription data
        await this.env.USERS_KV.put(
          `ba_token:${baToken}`,
          JSON.stringify({
            subscriptionId: subscription.id,
            userId,
            tier,
            addons,
            createdAt: new Date().toISOString(),
          }),
          { expirationTtl: 3600 } // Expire after 1 hour
        );
      }

      await this.env.USERS_KV.put(
        `subscription:${userId}`,
        JSON.stringify(subscriptionRecord)
      );

      return {
        ...subscriptionRecord,
        approvalUrl: subscription.links.find((link) => link.rel === "approve")
          ?.href,
      };
    } catch (error) {
      console.error("Error creating PayPal subscription:", error);
      throw error;
    }
  }

  async updateSubscriptionAddons(subscriptionId, addons) {
    try {
      const accessToken = await this.getAccessToken();
      const subscription = await this.getSubscriptionDetails(subscriptionId);
      const subscriptionData = await this.getSubscriptionData(subscriptionId);

      const tierConfig = await this.tierService.getTierSettings();
      const tierSettings = tierConfig.config[subscriptionData.tier];

      // Calculate new price with selected add-ons
      let newPrice = tierSettings.price;
      if (addons.addon1) newPrice += tierSettings.addon1_price;
      if (addons.addon2) newPrice += tierSettings.addon2_price;

      // Update subscription price
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}/revise`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify({
            plan_id: subscription.plan_id,
            shipping_amount: {
              currency_code: "USD",
              value: newPrice.toFixed(2),
            },
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update subscription");
      }

      // Update local subscription record
      await this.updateSubscriptionRecord(subscriptionId, {
        addons,
        totalPrice: newPrice,
      });

      return {
        success: true,
        subscriptionId,
        newPrice,
        addons,
      };
    } catch (error) {
      console.error("Error updating subscription addons:", error);
      throw error;
    }
  }

  async getAccessToken() {
    try {
      const response = await fetch(`${this.baseURL}/v1/oauth2/token`, {
        method: "POST",
        headers: {
          Accept: "application/json",
          Authorization: `Basic ${btoa(
            `${this.clientId}:${this.clientSecret}`
          )}`,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: "grant_type=client_credentials",
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error_description || "Failed to get access token");
      }

      return data.access_token;
    } catch (error) {
      console.error("PayPal access token error:", error);
      throw error;
    }
  }

  async getSubscriptionStatus(subscriptionId) {
    try {
      // Get PayPal access token
      const accessToken = await this.getAccessToken();

      // Fetch subscription details from PayPal
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to get subscription status");
      }

      const subscription = await response.json();

      // Find the subscription in our KV store
      const { keys } = await this.env.USERS_KV.list({
        prefix: "subscription:",
      });

      let localSubscription = null;
      let userId = null;

      // Search for the subscription in our KV store
      for (const key of keys) {
        const record = await this.env.USERS_KV.get(key.name, "json");
        if (record && record.subscriptionId === subscriptionId) {
          localSubscription = record;
          userId = key.name.split(":")[1];
          break;
        }
      }

      // Combine PayPal data with our local data
      return {
        subscriptionId: subscription.id,
        status: subscription.status,
        planId: subscription.plan_id,
        startTime: subscription.start_time,
        nextBillingTime: subscription.billing_info?.next_billing_time,
        lastPaymentTime: subscription.billing_info?.last_payment?.time,
        failedPayments: subscription.billing_info?.failed_payments_count || 0,
        tier: localSubscription?.tier,
        price: localSubscription?.price,
        localStatus: localSubscription?.status,
        createdAt: localSubscription?.createdAt,
        activatedAt: localSubscription?.activatedAt,
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error getting subscription status:", error);
      throw error;
    }
  }

  async verifyWebhookSignature(headers, body) {
    try {
      const accessToken = await this.getAccessToken();

      const verificationData = {
        auth_algo: headers.get("paypal-auth-algo"),
        cert_url: headers.get("paypal-cert-url"),
        transmission_id: headers.get("paypal-transmission-id"),
        transmission_sig: headers.get("paypal-transmission-sig"),
        transmission_time: headers.get("paypal-transmission-time"),
        webhook_id: this.env.PAYPAL_WEBHOOK_ID,
        webhook_event: body,
      };

      const response = await fetch(
        `${this.baseURL}/v1/notifications/verify-webhook-signature`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify(verificationData),
        }
      );

      const data = await response.json();
      return data.verification_status === "SUCCESS";
    } catch (error) {
      console.error("Error verifying webhook signature:", error);
      return false;
    }
  }

  async activateSubscription(subscriptionId) {
    try {
      const accessToken = await this.getAccessToken();

      // Get subscription details from PayPal
      const response = await fetch(
        `${this.baseURL}/v1/billing/subscriptions/${subscriptionId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to get subscription details from PayPal");
      }

      const subscription = await response.json();

      // Get subscription data from KV
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found in KV store");
      }

      // Update subscription status
      const updatedSubscriptionData = {
        ...subscriptionData,
        status: subscription.status,
        startTime: subscription.start_time,
        nextBillingTime: subscription.billing_info?.next_billing_time,
        updatedAt: new Date().toISOString(),
      };

      await this.env.USERS_KV.put(
        `subscription:${subscriptionId}`,
        JSON.stringify(updatedSubscriptionData)
      );

      return updatedSubscriptionData;
    } catch (error) {
      console.error("Error activating subscription:", error);
      throw error;
    }
  }

  async _createPlan(planData) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(`${this.baseURL}/v1/billing/plans`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "PayPal-Request-Id": `plan_${Date.now()}`,
        },
        body: JSON.stringify(planData),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create billing plan");
      }

      return data;
    } catch (error) {
      console.error("Error creating PayPal billing plan:", error);
      throw error;
    }
  }

  _generateProductDescription(tierConfig, addons) {
    let description = `${tierConfig.name} features:\n`;
    description += tierConfig.features.join(", ") + "\n";

    if (addons.addon1) {
      description += "\nAddon 1 features:\n";
      description += tierConfig.addon1_detail.join(", ");
    }

    if (addons.addon2) {
      description += "\nAddon 2 features:\n";
      description += tierConfig.addon2_detail.join(", ");
    }

    return description;
  }

  async _createSubscriptionWithPlan(planId, userId) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(`${this.baseURL}/v1/billing/subscriptions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "PayPal-Request-Id": `sub_${Date.now()}`,
        },
        body: JSON.stringify({
          plan_id: planId,
          subscriber: {
            payer_id: userId,
          },
          application_context: {
            return_url: `${this.env.APP_URL}/api/subscriptions/success`,
            cancel_url: `${this.env.APP_URL}/api/subscriptions/cancel`,
            payment_method: {
              payer_selected: "PAYPAL",
              payee_preferred: "IMMEDIATE_PAYMENT_REQUIRED",
            },
            payment_methods: {
              payment_types: [
                {
                  type: "QRIS",
                },
              ],
            },
          },
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || "Failed to create subscription");
      }

      return data;
    } catch (error) {
      console.error("Error creating PayPal subscription:", error);
      throw error;
    }
  }

  async createOneTimePurchase(email, tier, addons = {}, amount) {
    try {
      const tierConfig = await this.tierService.getTierSettings();
      const tierSettings = tierConfig.config[tier];

      if (!tierSettings) {
        throw new Error(`Invalid tier: ${tier}`);
      }

      // Create PayPal order with provided amount
      const order = await this._createOrder({
        intent: "CAPTURE",
        purchase_units: [
          {
            amount: {
              currency_code: "USD",
              value: amount.toFixed(2),
            },
            description: `Lifetime access to ${tier} tier`,
          },
        ],
      });

      // Store purchase info in KV
      await this.env.USERS_KV.put(
        `purchase:${order.id}`,
        JSON.stringify({
          orderId: order.id,
          email,
          tier,
          addons,
          amount,
          status: "pending",
          createdAt: new Date().toISOString(),
        }),
        { expirationTtl: 3600 } // Expire after 1 hour
      );

      return {
        orderId: order.id,
        approvalUrl: order.links.find((link) => link.rel === "approve")?.href,
        tier,
        totalPrice: amount,
        status: "pending",
      };
    } catch (error) {
      console.error("Error creating one-time purchase:", error);
      throw error;
    }
  }

  async _createOrder(orderData) {
    const accessToken = await this.getAccessToken();

    // Add application_context to orderData
    const orderWithContext = {
      ...orderData,
      application_context: {
        return_url: `${
          this.env.APP_URL || "http://localhost:3000"
        }/api/purchases/success`,
        cancel_url: `${
          this.env.APP_URL || "http://localhost:3000"
        }/dashboard?purchase_status=cancelled`,
        brand_name: "Your Brand",
        landing_page: "LOGIN",
        user_action: "PAY_NOW",
        shipping_preference: "NO_SHIPPING",
      },
    };

    const response = await fetch(`${this.baseURL}/v2/checkout/orders`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(orderWithContext),
    });

    if (!response.ok) {
      throw new Error(`PayPal order creation failed: ${await response.text()}`);
    }

    return response.json();
  }

  async capturePayment(orderId) {
    const accessToken = await this.getAccessToken();
    const response = await fetch(
      `${this.baseURL}/v2/checkout/orders/${orderId}/capture`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Payment capture failed: ${await response.text()}`);
    }

    return response.json();
  }

  async getOrderStatus(orderId) {
    try {
      const accessToken = await this.getAccessToken();
      const response = await fetch(
        `${this.baseURL}/v2/checkout/orders/${orderId}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get order status: ${errorText}`);
      }

      const orderData = await response.json();

      // Get local purchase data from KV
      let localPurchaseData = null;
      try {
        const purchaseDataStr = await this.env.USERS_KV.get(`purchase:${orderId}`);
        if (purchaseDataStr) {
          localPurchaseData = JSON.parse(purchaseDataStr);
        }
      } catch (error) {
        console.log("No local purchase data found for order:", orderId);
      }

      // Combine PayPal order data with local data
      return {
        orderId: orderData.id,
        status: orderData.status,
        intent: orderData.intent,
        createdTime: orderData.create_time,
        updateTime: orderData.update_time,
        purchaseUnits: orderData.purchase_units,
        payer: orderData.payer,
        links: orderData.links,
        // Local data if available
        localData: localPurchaseData ? {
          email: localPurchaseData.email,
          tier: localPurchaseData.tier,
          addons: localPurchaseData.addons,
          amount: localPurchaseData.amount,
          createdAt: localPurchaseData.createdAt
        } : null
      };
    } catch (error) {
      console.error("Error getting order status:", error);
      throw error;
    }
  }
}
