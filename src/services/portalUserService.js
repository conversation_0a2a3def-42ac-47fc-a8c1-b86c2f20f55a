// src/services/portalUserService.js
import { TokenService } from "./tokenService.js";

export class PortalUserService {
  constructor(env) {
    this.env = env;
    this.namespace = env.USERS_KV; // Use same KV namespace as regular users
    this.tokenService = new TokenService(env);
  }

  async createPortalUser(userData) {
    console.log("Creating portal user:", userData);

    try {
      const userId = `portal_${Date.now()}_${Math.random()
        .toString(36)
        .slice(2, 11)}`;
      // Only generate activation token for non-Google signups
      const activationToken = userData.googleId
        ? null
        : `act_${Date.now()}_${Math.random().toString(36).slice(2, 17)}`;

      console.log("Generated activationToken:", activationToken);

      const user = {
        id: userId,
        email: userData.email,
        password: userData.password, // This will be a random UUID for Google users
        type: "portal",
        createdAt: new Date().toISOString(),
        lastLogin: null,
        isActive: userData.googleId ? 1 : 0, // Activate Google users immediately
        activationToken: activationToken,
        googleId: userData.googleId || null,
        picture: userData.picture || null, // Simpan picture dari Google
        profile: {
          username: userData.email.split("@")[0],
          displayName: userData.displayName || userData.email.split("@")[0],
        },
      };

      console.log("User object before saving:", JSON.stringify(user, null, 2));

      // Store user data
      await this.namespace.put(`portal_user:${userId}`, JSON.stringify(user));

      // Store email index for lookups
      await this.namespace.put(`portal_email_index:${userData.email}`, userId);

      // Store activation token for lookup (only for non-Google users)
      if (activationToken) {
        await this.namespace.put(`activation_token:${activationToken}`, userId);
      }

      console.log("✅ Portal user created successfully:", userId);
      console.log("Returning user object:", JSON.stringify(user, null, 2));

      // Validate the user object before returning (only check for non-Google users)
      if (!userData.googleId && !user.activationToken) {
        console.error("❌ CRITICAL: activationToken missing from user object!");
        throw new Error("Activation token generation failed");
      }

      return user;
    } catch (error) {
      console.error("❌ Error creating portal user:", error);
      throw error;
    }
  }

  async getPortalUserByEmail(email) {
    try {
      const userId = await this.namespace.get(`portal_email_index:${email}`);
      if (!userId) {
        return null;
      }

      const userData = await this.namespace.get(`portal_user:${userId}`);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("❌ Error fetching portal user by email:", error);
      return null;
    }
  }

  async getPortalUserById(userId) {
    try {
      const userData = await this.namespace.get(`portal_user:${userId}`);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("❌ Error fetching portal user by ID:", error);
      return null;
    }
  }

  async updatePortalUser(userId, updateData) {
    try {
      const existingUser = await this.getPortalUserById(userId);
      if (!existingUser) {
        throw new Error("Portal user not found");
      }

      const updatedUser = {
        ...existingUser,
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      await this.namespace.put(
        `portal_user:${userId}`,
        JSON.stringify(updatedUser)
      );
      console.log("✅ Portal user updated successfully:", userId);
      return updatedUser;
    } catch (error) {
      console.error("❌ Error updating portal user:", error);
      throw error;
    }
  }

  async updateLastLogin(userId) {
    try {
      const user = await this.getPortalUserById(userId);
      if (user) {
        user.lastLogin = new Date().toISOString();
        await this.namespace.put(`portal_user:${userId}`, JSON.stringify(user));
      }
    } catch (error) {
      console.error("❌ Error updating last login:", error);
    }
  }

  async validatePortalCredentials(email, password) {
    try {
      const user = await this.getPortalUserByEmail(email);
      if (!user) {
        return { valid: false, message: "User not found" };
      }

      if (user.isActive === 0) {
        return {
          valid: false,
          message:
            "Account not activated. Please check your email and click the activation link.",
        };
      }

      // In production, use proper password hashing comparison
      if (user.password !== password) {
        return { valid: false, message: "Invalid credentials" };
      }

      return { valid: true, user };
    } catch (error) {
      console.error("❌ Error validating portal credentials:", error);
      return { valid: false, message: "Authentication error" };
    }
  }

  async getAllPortalUsers() {
    try {
      const keys = await this.namespace.list({ prefix: "portal_user:" });
      const users = [];

      for (const key of keys.keys) {
        const userData = await this.namespace.get(key.name);
        if (userData) {
          const user = JSON.parse(userData);
          // Don't return password in list
          delete user.password;
          users.push(user);
        }
      }

      return users;
    } catch (error) {
      console.error("❌ Error fetching all portal users:", error);
      return [];
    }
  }

  async deletePortalUser(userId) {
    try {
      const user = await this.getPortalUserById(userId);
      if (!user) {
        throw new Error("Portal user not found");
      }

      console.log(
        `🗑️ Starting comprehensive deletion for user: ${user.email} (${userId})`
      );

      // Track deletion operations
      const deletionOperations = [];

      // 1. Delete main portal user data
      deletionOperations.push(
        this.namespace.delete(`portal_user:${userId}`),
        this.namespace.delete(`portal_email_index:${user.email}`)
      );

      // 2. Delete activation token if exists
      if (user.activationToken) {
        console.log(`🔑 Deleting activation token: ${user.activationToken}`);
        deletionOperations.push(
          this.namespace.delete(`activation_token:${user.activationToken}`)
        );
      }

      // 3. Delete portal credentials (from UserService.createPortalUser)
      deletionOperations.push(
        this.namespace.delete(`portal_credentials:${userId}`)
      );

      // 4. Delete general user data (if stored in both systems)
      deletionOperations.push(
        this.namespace.delete(`user:${userId}`),
        this.namespace.delete(`email:${user.email}`)
      );

      // 5. Delete tier-related data
      deletionOperations.push(
        this.namespace.delete(`t_setting:user_tier:${userId}`),
        this.namespace.delete(`t_setting:quota_usage:${userId}`),
        this.namespace.delete(`t_setting:email_tier:${user.email}`)
      );

      // 6. Delete subscription data
      deletionOperations.push(this.namespace.delete(`subscription:${userId}`));

      // 7. Delete usage history data
      console.log(`📊 Cleaning up usage history for user: ${userId}`);

      // Get all usage history keys for this user
      const usageHistoryKeys = await this.namespace.list({
        prefix: `usage_history:${userId}:`,
      });

      for (const key of usageHistoryKeys.keys) {
        deletionOperations.push(this.namespace.delete(key.name));
      }

      // Get all daily usage keys for this user
      const dailyUsageKeys = await this.namespace.list({
        prefix: `daily_usage:${userId}:`,
      });

      for (const key of dailyUsageKeys.keys) {
        deletionOperations.push(this.namespace.delete(key.name));
      }

      // 8. Clean up email queue items
      console.log(`📧 Cleaning up email queue for user: ${user.email}`);

      const emailQueueKeys = await this.namespace.list({
        prefix: `email_queue:`,
      });

      for (const key of emailQueueKeys.keys) {
        try {
          const emailData = await this.namespace.get(key.name, "json");
          if (emailData && emailData.email === user.email) {
            deletionOperations.push(this.namespace.delete(key.name));
          }
        } catch (error) {
          console.warn(
            `⚠️ Could not check email queue item ${key.name}:`,
            error
          );
        }
      }

      // 9. Clean up used password reset tokens (best effort)
      console.log(`🔐 Cleaning up password reset tokens...`);

      const resetTokenKeys = await this.namespace.list({
        prefix: `used_reset_token:`,
      });

      // This is harder to clean up as we don't have user association in the key
      // We'll skip this for now as these tokens have TTL anyway

      // 10. Clean up any API keys if portal user somehow has them
      if (user.domains && user.domains.length > 0) {
        console.log(`🔗 Cleaning up domains and API keys...`);

        for (const domain of user.domains) {
          deletionOperations.push(
            this.namespace.delete(`domain:${domain.domain}`),
            this.namespace.delete(`api_key:${domain.api_key}`),
            this.namespace.delete(`apikey:${domain.api_key}`)
          );
        }
      }

      // Execute all deletion operations
      console.log(
        `🗑️ Executing ${deletionOperations.length} deletion operations...`
      );
      await Promise.allSettled(deletionOperations);

      console.log(
        "✅ Portal user and all associated data deleted successfully:",
        userId
      );

      return {
        success: true,
        userId,
        email: user.email,
        deletedOperations: deletionOperations.length,
        message: "User and all associated data deleted successfully",
      };
    } catch (error) {
      console.error("❌ Error deleting portal user:", error);
      throw error;
    }
  }

  async deletePortalUserByEmail(email) {
    try {
      console.log(`🔍 Looking up user by email: ${email}`);

      // Get user by email
      const user = await this.getPortalUserByEmail(email);
      if (!user) {
        throw new Error(`Portal user not found with email: ${email}`);
      }

      console.log(`✅ Found user: ${user.id} (${user.email})`);

      // Call the comprehensive delete method
      return await this.deletePortalUser(user.id);
    } catch (error) {
      console.error("❌ Error deleting portal user by email:", error);
      throw error;
    }
  }

  async verifyPortalUser(email, password) {
    const user = await this.getPortalUserByEmail(email);

    if (!user) {
      return null;
    }

    // IMPORTANT: Replace with a secure password hashing and comparison function
    if (user.password !== password) {
      return null;
    }

    await this.updateLastLogin(user.id);

    return user;
  }

  async activateUser(activationToken) {
    try {
      // Get user ID from activation token
      const userId = await this.namespace.get(
        `activation_token:${activationToken}`
      );
      if (!userId) {
        return { success: false, message: "Invalid activation token" };
      }

      // Get user data
      const userData = await this.namespace.get(`portal_user:${userId}`);
      if (!userData) {
        return { success: false, message: "User not found" };
      }

      const user = JSON.parse(userData);

      // Check if already activated
      if (user.isActive === 1) {
        return { success: false, message: "User already activated" };
      }

      // Activate user
      user.isActive = 1;
      user.activatedAt = new Date().toISOString();

      // Remove activation token from user object
      delete user.activationToken;

      // Update user data
      await this.namespace.put(`portal_user:${userId}`, JSON.stringify(user));

      // Delete activation token
      await this.namespace.delete(`activation_token:${activationToken}`);

      console.log("✅ User activated successfully:", user.email);
      return {
        success: true,
        message: "User activated successfully",
        user: user,
      };
    } catch (error) {
      console.error("❌ Error activating user:", error);
      return { success: false, message: "Error activating user" };
    }
  }

  async resetPassword(token, newPassword) {
    try {
      console.log("🔄 Starting password reset process...");
      console.log(`   Token provided: ${token ? "YES" : "NO"}`);
      console.log(`   New password provided: ${newPassword ? "YES" : "NO"}`);

      // Verify the password reset token
      const decodedToken = await this.tokenService.verifyPasswordResetToken(
        token
      );

      if (!decodedToken.success) {
        console.log("❌ Token verification failed:", decodedToken.error);
        return { success: false, message: "Invalid or expired token." };
      }

      const { userId } = decodedToken.data;
      console.log(`✅ Token verified for user ID: ${userId}`);

      // Get user data
      const user = await this.getPortalUserById(userId);

      if (!user) {
        console.log("❌ User not found for ID:", userId);
        return { success: false, message: "User not found." };
      }

      console.log(`✅ User found: ${user.email}`);

      // Check if token has already been used
      const usedTokenKey = `used_reset_token:${token}`;
      const isTokenUsed = await this.namespace.get(usedTokenKey);

      if (isTokenUsed) {
        console.log("❌ Token has already been used");
        return {
          success: false,
          message: "This password reset link has already been used.",
        };
      }

      // Mark the token as used with a 1-hour expiration to clean up KV
      await this.namespace.put(usedTokenKey, "true", { expirationTtl: 3600 });
      console.log("✅ Token marked as used");

      // Update the user's password
      const updatedUser = {
        ...user,
        password: newPassword, // In a real app, hash this password
        updatedAt: new Date().toISOString(),
      };

      await this.namespace.put(
        `portal_user:${userId}`,
        JSON.stringify(updatedUser)
      );

      console.log(`✅ Password reset successfully for user: ${user.email}`);
      return {
        success: true,
        message: "Password has been reset successfully.",
      };
    } catch (error) {
      console.error("❌ Error in resetPassword service:", error);
      return {
        success: false,
        message: "An error occurred during password reset.",
      };
    }
  }
}
