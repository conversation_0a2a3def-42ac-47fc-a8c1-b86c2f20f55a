import { PayPalService } from "../services/paypalService.js";
import { TierService as TierServiceD1 } from "../services/tierServiceD1.js";
import { ResponseService } from "../services/responseService.js";

export class WebhookController {
  constructor(env) {
    this.env = env;
    this.paypalService = new PayPalService(env);
    this.tierService = new TierServiceD1(env);
    this.responseService = new ResponseService();
  }

  async handlePayPalWebhook(request) {
    try {
      // Verify PayPal webhook signature
      const paypalSignature = request.headers.get("paypal-transmission-sig");
      const paypalCertUrl = request.headers.get("paypal-cert-url");
      const paypalTransmissionId = request.headers.get(
        "paypal-transmission-id"
      );
      const paypalTransmissionTime = request.headers.get(
        "paypal-transmission-time"
      );

      if (
        !paypalSignature ||
        !paypalCertUrl ||
        !paypalTransmissionId ||
        !paypalTransmissionTime
      ) {
        throw new Error("Missing required PayPal webhook headers");
      }

      // Parse webhook data
      const webhookData = await request.json();
      console.log("Received PayPal webhook:", webhookData);

      const eventType = webhookData.event_type;
      const resourceId = webhookData.resource.id; // subscription ID

      switch (eventType) {
        case "BILLING.SUBSCRIPTION.ACTIVATED":
          await this._handleSubscriptionActivated(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.UPDATED":
          await this._handleSubscriptionUpdated(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.CANCELLED":
          await this._handleSubscriptionCancelled(
            resourceId,
            webhookData.resource
          );
          break;
        case "BILLING.SUBSCRIPTION.EXPIRED":
          await this._handleSubscriptionExpired(
            resourceId,
            webhookData.resource
          );
          break;
        default:
          console.log("Unhandled webhook event type:", eventType);
      }

      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error("Error handling PayPal webhook:", error);
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to process webhook",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async _handleSubscriptionActivated(subscriptionId, resource) {
    try {
      console.log("Handling subscription activated:", subscriptionId);

      // Get subscription details from KV
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }

      // Get user ID from subscription data
      const userId = subscriptionData.userId;
      if (!userId) {
        throw new Error("User ID not found in subscription data");
      }

      // Update user's tier
      await this.tierService.updateUserTier(userId, {
        tier: subscriptionData.tier,
        subscriptionId: subscriptionId,
        addons: subscriptionData.addons,
        status: "active",
        startDate: new Date().toISOString(),
        expirationDate: resource.billing_info?.next_billing_time || null,
      });

      console.log("Successfully activated subscription for user:", userId);
    } catch (error) {
      console.error("Error handling subscription activation:", error);
      throw error;
    }
  }

  async _handleSubscriptionUpdated(subscriptionId, resource) {
    try {
      console.log("Handling subscription updated:", subscriptionId);

      // Get subscription details from KV
      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }

      // Update subscription status and next billing date
      await this.env.USERS_KV.put(
        `subscription:${subscriptionId}`,
        JSON.stringify({
          ...subscriptionData,
          status: resource.status,
          nextBillingTime: resource.billing_info?.next_billing_time || null,
          updatedAt: new Date().toISOString(),
        })
      );

      console.log("Successfully updated subscription:", subscriptionId);
    } catch (error) {
      console.error("Error handling subscription update:", error);
      throw error;
    }
  }

  async _handleSubscriptionCancelled(subscriptionId, resource) {
    try {
      console.log("Handling subscription cancelled:", subscriptionId);

      const subscriptionData = await this.env.USERS_KV.get(
        `subscription:${subscriptionId}`,
        "json"
      );
      if (!subscriptionData) {
        throw new Error("Subscription data not found");
      }

      // Reset user to starter tier
      await this.tierService.updateUserTier(subscriptionData.userId, {
        tier: "starter",
        status: "cancelled",
        subscriptionId: null,
        addons: { addon1: false, addon2: false },
      });

      console.log(
        "Successfully cancelled subscription for user:",
        subscriptionData.userId
      );
    } catch (error) {
      console.error("Error handling subscription cancellation:", error);
      throw error;
    }
  }

  async _handleSubscriptionExpired(subscriptionId, resource) {
    // Handle similar to cancellation
    await this._handleSubscriptionCancelled(subscriptionId, resource);
  }

  async getWebhookStatus(request) {
    try {
      // Get PayPal access token to verify connection
      const accessToken = await this.paypalService.getAccessToken();

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            paypal: {
              connected: !!accessToken,
              sandbox: this.env.PAYPAL_SANDBOX === "true",
              webhookId: !!this.env.PAYPAL_WEBHOOK_ID,
            },
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to get webhook status",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }

  async getRecentEvents(request) {
    try {
      // For development/testing only
      const events =
        (await this.env.USERS_KV.get("webhook_events", "json")) || [];
      return new Response(
        JSON.stringify({
          success: true,
          data: events,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: error.message || "Failed to get recent events",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }
}
