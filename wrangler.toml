name = "backend"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]
main = "src/index.js"

# D1 Database configuration
[[d1_databases]]
binding = "DB"
database_name = "supersense-db"
database_id = "9d7bfd71-f220-492b-bc46-afac3dca20bb"

# Keep KV for migration period (will be removed after migration)
[[kv_namespaces]]
binding = "USERS_KV"
id = "1cee420f00eb4f9a91eb59d23fccfa6d"
preview_id = "e7f1bbadf2fd4685963790b17f8e3aec"

[vars]
# Default values (will be overridden by .dev.vars in development)
ZEPTO_FROM_ADDRESS = "<EMAIL>"
ZEPTO_TOKEN = "Zoho-enczapikey wSsVR61+8kb1X/h0nGWoIrxunVtRUl/xRxx7iVH06HWtGa3E9Mc+xkbNA1ehFKJMGG5qEjsVo78tnxZWgTUHh9l7ygpRDyiF9mqRe1U4J3x17qnvhDzKXm5ZkhWOJY8Pzw9jmWZkEcgg+g=="
PAYPAL_CLIENT_ID = "AVb3laFGn2dcy1b-tG4aUaT5IRKxJTJGjABW3FgPcPLsDoQNMTp62XICUIpkiLwugTkPSdG3-uRLsHzT"
PAYPAL_CLIENT_SECRET = "EDCAlFdG67Ipe46jQ563Q38ImSxqPSdJkvNUeBWvcTcFpr1ljmhPGatO6RJ6sNZ5xOp0V2wlmP2U-huK"
PAYPAL_SANDBOX = "true"
APP_URL = "https://fb1333dc86c8.ngrok-free.app"
FRONTEND_URL = "https://supersense-ett.pages.dev"
PAYPAL_WEBHOOK_ID = "your_webhook_id_here"
#PAYPAL_CLIENT_ID=AQkV0Vf8_f2T9VKKuZvqzjX-OozqzxWXVKHr_AO6MvD7QN6W98siX1Ayn9E_iGWP1nNX0HjWyK-3fWq4
#PAYPAL_CLIENT_SECRET=EHrcjwAkiGQBX8xZ5PJhgz9_9eVLUY6NqnXHVj0vgEb_F1JLghzUXpmfKnQXqFXyUKtaZR_EH8VxJOJD
#PAYPAL_SANDBOX=true
XENDIT_SECRET_KEY = "xnd_development_tEbCNlSs6vqusUzVGUv3FKtW3ekr8UtpIR4fUY1gXJYCsxBUh7oy0t5jc1snh"
XENDIT_WEBHOOK_TOKEN = "Dvy2I8HPFPTZonSXO2XqUWe6SOUbyNlqSwsxzeCDpCG5OLGy"
JWT_SECRET = "1z4rg4nt3ng"
GOOGLE_CLIENT_ID = "586490779285-4hbe66vj9kifm1dbc2vm02n8jpogdq61.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET = "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"
GOOGLE_REDIRECT_URI = "https://fb1333dc86c8.ngrok-free.app/api/portal/auth/google/callback"
RATE_IDR=15000


[triggers]
crons = ["*/5 * * * *"]  # Runs every 5 minutes

[dev]
port = 3000
